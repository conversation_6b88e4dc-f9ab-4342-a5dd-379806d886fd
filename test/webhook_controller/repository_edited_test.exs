defmodule RepobotWeb.WebhookController.RepositoryEditedTest do
  use RepobotWeb.ConnCase, async: false
  use Oban.Testing, repo: Repobot.Repo
  use Repobot.Test.Fixtures

  import Mox

  alias Repobot.{Events, Repositories}
  alias Repobot.Workers.EventHandlers.GitHub.RepositoryEdited

  setup :verify_on_exit!

  setup do
    # Set up Oban test mode
    :ok = Oban.Testing.with_testing_mode(:inline, fn -> :ok end)

    # Setup signature verifier mock
    Repobot.Test.SignatureVerifierMock
    |> stub(:verify_signature, fn _conn -> :ok end)

    organization = organization_fixture()
    user = user_fixture(%{default_organization_id: organization.id})

    # Create a repository with GitHub data
    github_id = 123_456_789

    repository_data = %{
      "id" => github_id,
      "name" => "test-repo",
      "full_name" => "testorg/test-repo",
      "owner" => %{"login" => "testorg"},
      "private" => false,
      "description" => "Original description",
      "language" => "Elixir",
      "fork" => false
    }

    repository =
      create_repository(%{
        organization_id: organization.id,
        name: "test-repo",
        full_name: "testorg/test-repo",
        owner: "testorg",
        data: repository_data
      })

    %{user: user, organization: organization, repository: repository}
  end

  describe "repository edited webhook" do
    test "creates event and schedules worker for repository edit", %{
      conn: conn,
      repository: repository
    } do
      payload = %{
        "action" => "edited",
        "repository" => %{
          "id" => repository.data["id"],
          "name" => "test-repo",
          "full_name" => "testorg/test-repo",
          "owner" => %{"login" => "testorg"},
          "private" => false,
          "description" => "Updated description",
          "language" => "Elixir",
          "fork" => false
        }
      }

      conn =
        conn
        |> put_req_header("x-github-event", "repository")

      # Make the webhook request
      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify event was created
      events = Events.list_events()
      assert length(events) == 1

      event = List.first(events)
      assert event.type == "github.repository.edited"
      assert event.organization_id == repository.organization_id
      assert event.repository_id == repository.id
      assert event.payload == payload

      # Verify worker was scheduled and executed (in inline mode)
      # The repository should be updated
      updated_repository = Repositories.get_repository!(repository.id)
      assert get_in(updated_repository.data, ["description"]) == "Updated description"

      # Verify event status was updated to completed
      updated_event = Events.get_event!(event.id)
      assert updated_event.status == "completed"
    end

    test "handles repository not found in database", %{conn: conn} do
      payload = %{
        "action" => "edited",
        "repository" => %{
          # Non-existent repository
          "id" => 999_999_999,
          "name" => "test-repo",
          "full_name" => "testorg/test-repo",
          "owner" => %{"login" => "testorg"},
          "private" => false,
          "description" => "Updated description",
          "language" => "Elixir",
          "fork" => false
        }
      }

      conn =
        conn
        |> put_req_header("x-github-event", "repository")

      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify no event was created since repository wasn't found
      events = Events.list_events()
      assert length(events) == 0
    end

    test "worker handles missing event", %{repository: _repository} do
      # Create a job with non-existent event_id (using a valid binary_id format)
      non_existent_id = Ecto.UUID.generate()

      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.RepositoryEdited",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => non_existent_id}
      }

      result = RepositoryEdited.perform(job)
      assert {:error, error_message} = result
      assert error_message == "Event not found: #{non_existent_id}"
    end

    test "worker handles invalid job arguments", %{repository: _repository} do
      # Create a job without event_id
      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.RepositoryEdited",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"invalid" => "args"}
      }

      result = RepositoryEdited.perform(job)
      assert {:error, "Missing event_id in job arguments"} = result
    end
  end
end
