defmodule Repobot.Workers.EventHandlers.GitHub.RepositoryRenamed do
  @moduledoc """
  Oban worker for handling GitHub repository renamed events.
  
  This worker processes repository rename events that have been stored
  in the events table and updates the repository information in the database.
  """

  use Repobot.Workers.Worker, queue: :default, max_attempts: 3

  alias Repobot.{Events, Repositories}

  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"event_id" => event_id}} = job) do
    log_job_start(job)

    case Events.get_event(event_id) do
      nil ->
        reason = "Event not found: #{event_id}"
        log_job_error(job, reason)
        {:error, reason}

      event ->
        case process_repository_renamed_event(event) do
          :ok ->
            # Update event status to completed
            Events.update_event_status(event, "completed")
            log_job_success(job, %{event_id: event_id, event_type: event.type})
            :ok

          {:error, reason} ->
            # Update event status to failed
            Events.update_event_status(event, "failed")
            log_job_error(job, reason, %{event_id: event_id, event_type: event.type})
            {:error, reason}
        end
    end
  end

  def perform(%Oban.Job{} = job) do
    log_job_start(job)
    reason = "Missing event_id in job arguments"
    log_job_error(job, reason, %{received_args: job.args})
    {:error, reason}
  end

  # Process the repository renamed event
  defp process_repository_renamed_event(%Events.Event{} = event) do
    payload = event.payload
    repository_data = payload["repository"]
    changes = payload["changes"]

    github_id = repository_data["id"]
    new_full_name = repository_data["full_name"]
    old_name = get_in(changes, ["repository", "name", "from"])

    Logger.info("Processing repository rename event",
      event_id: event.id,
      github_id: github_id,
      old_name: old_name,
      new_full_name: new_full_name
    )

    case Repositories.get_repository_by_github_id(github_id) do
      nil ->
        Logger.info("Repository not found in database, skipping rename update",
          event_id: event.id,
          github_id: github_id,
          old_name: old_name,
          new_full_name: new_full_name
        )

        :ok

      repo ->
        Logger.info("Found repository for rename update",
          event_id: event.id,
          github_id: github_id,
          repository_id: repo.id,
          old_full_name: repo.full_name,
          new_full_name: new_full_name
        )

        case Repositories.update_repository_from_webhook(repo, repository_data) do
          {:ok, updated_repo} ->
            Logger.info("Repository successfully renamed via event worker",
              event_id: event.id,
              github_id: github_id,
              repository_id: updated_repo.id,
              old_full_name: repo.full_name,
              new_full_name: updated_repo.full_name
            )

            :ok

          {:error, reason} ->
            Logger.error("Failed to rename repository from event worker",
              event_id: event.id,
              github_id: github_id,
              repository_id: repo.id,
              old_full_name: repo.full_name,
              new_full_name: new_full_name,
              error: inspect(reason)
            )

            {:error, reason}
        end
    end
  end
end
