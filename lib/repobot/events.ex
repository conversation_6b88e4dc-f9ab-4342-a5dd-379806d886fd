defmodule Repobot.Events do
  @moduledoc """
  The Events context.
  """

  import Ecto.Query, warn: false
  require Logger
  alias <PERSON><PERSON>ot.Repo
  alias Repobot.Events.Event

  @doc """
  Returns the list of events.

  ## Examples

      iex> list_events()
      [%Event{}, ...]

  """
  def list_events do
    Repo.all(Event)
  end

  @doc """
  Gets a single event.

  Returns `nil` if the Event does not exist.

  ## Examples

      iex> get_event(123)
      %Event{}

      iex> get_event(456)
      nil

  """
  def get_event(id), do: Repo.get(Event, id)

  @doc """
  Gets a single event.

  Raises `Ecto.NoResultsError` if the Event does not exist.

  ## Examples

      iex> get_event!(123)
      %Event{}

      iex> get_event!(456)
      ** (Ecto.NoResultsError)

  """
  def get_event!(id), do: Repo.get!(Event, id)

  @doc """
  Creates a event.

  ## Examples

      iex> create_event(%{field: value})
      {:ok, %Event{}}

      iex> create_event(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_event(attrs \\ %{}) do
    %Event{}
    |> Event.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates an event with optional user_id, repository_id and status.
  Logs the event in the database and returns the created event.

  ## Examples

      iex> log_event("repobot.sync", %{repository: "owner/repo"}, organization_id, user_id, repository_id)
      {:ok, %Event{}}

  """
  def log_event(
        type,
        payload,
        organization_id,
        user_id \\ nil,
        repository_id \\ nil,
        status \\ nil
      ) do
    attrs = %{
      type: type,
      payload: payload,
      organization_id: organization_id,
      user_id: user_id,
      repository_id: repository_id,
      status: status
    }

    create_event(attrs)
  end

  @doc """
  Logs a GitHub webhook event with the appropriate namespace.

  ## Examples

      iex> log_github_event("push", %{repository: "owner/repo"}, organization_id, user_id, repository_id)
      {:ok, %Event{}}

  """
  def log_github_event(
        github_event_type,
        payload,
        organization_id,
        user_id \\ nil,
        repository_id \\ nil,
        status \\ nil
      ) do
    # Map GitHub event type to namespaced event type
    namespaced_type = "github.#{github_event_type}"

    # For pull_request events, we can extract the action to create a more specific type
    namespaced_type =
      if github_event_type == "pull_request" and Map.has_key?(payload, "action") do
        "github.pull_request.#{payload["action"]}"
      else
        namespaced_type
      end

    log_event(namespaced_type, payload, organization_id, user_id, repository_id, status)
  end

  @doc """
  Logs a Repobot internal event with the appropriate namespace.

  ## Examples

      iex> log_repobot_event("sync", %{template_repo: "owner/repo"}, organization_id, repository_id)
      {:ok, %Event{}}

  """
  def log_repobot_event(
        event_type,
        payload,
        organization_id,
        user_id \\ nil,
        repository_id \\ nil,
        status \\ nil
      ) do
    namespaced_type = "repobot.#{event_type}"
    log_event(namespaced_type, payload, organization_id, user_id, repository_id, status)
  end

  @doc """
  Creates an event and schedules a worker to process it.

  This function creates an event in the database and then schedules
  an appropriate Oban worker to handle the event processing.

  ## Examples

      iex> create_and_schedule_event("github.push", payload, organization_id, nil, repository_id)
      {:ok, %Event{}}

  """
  def create_and_schedule_event(
        type,
        payload,
        organization_id,
        user_id \\ nil,
        repository_id \\ nil,
        status \\ "pending"
      ) do
    with {:ok, event} <-
           log_event(type, payload, organization_id, user_id, repository_id, status),
         {:ok, _job} <- schedule_event_worker(event) do
      {:ok, event}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  Schedules an Oban worker to process the given event.

  Workers are resolved based on event types:
  - `github.push` -> `Workers.EventHandlers.GitHub.Push`
  - `github.repository.renamed` -> `Workers.EventHandlers.GitHub.RepositoryRenamed`

  If no worker is found for the event type, the event remains unhandled.
  """
  def schedule_event_worker(%Event{} = event) do
    case resolve_worker_module(event.type) do
      {:ok, worker_module} ->
        worker_module.new(%{"event_id" => event.id})
        |> Oban.insert()

      {:error, :no_worker} ->
        Logger.info("No worker found for event type: #{event.type}", event_id: event.id)
        {:ok, :no_worker}
    end
  end

  @doc """
  Updates the status of an event.
  """
  def update_event_status(%Event{} = event, status) do
    event
    |> Event.changeset(%{status: status})
    |> Repo.update()
  end

  # Private helper to resolve worker module from event type
  defp resolve_worker_module("github." <> github_event_type) do
    case github_event_type do
      "repository.created" ->
        {:ok, Repobot.Workers.EventHandlers.GitHub.RepositoryCreated}

      "repository.renamed" ->
        {:ok, Repobot.Workers.EventHandlers.GitHub.RepositoryRenamed}

      "repository.edited" ->
        {:ok, Repobot.Workers.EventHandlers.GitHub.RepositoryEdited}

      _ ->
        {:error, :no_worker}
    end
  end

  defp resolve_worker_module(_type) do
    {:error, :no_worker}
  end

  @doc """
  Deletes a event.

  ## Examples

      iex> delete_event(event)
      {:ok, %Event{}}

      iex> delete_event(event)
      {:error, %Ecto.Changeset{}}

  """
  def delete_event(%Event{} = event) do
    Repo.delete(event)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking event changes.

  ## Examples

      iex> change_event(event)
      %Ecto.Changeset{data: %Event{}}

  """
  def change_event(%Event{} = event, attrs \\ %{}) do
    Event.changeset(event, attrs)
  end
end
